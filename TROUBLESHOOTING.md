# دليل استكشاف الأخطاء وإصلاحها - Troubleshooting Guide

## 🔧 المشاكل الشائعة وحلولها

### 1. خطأ: Could not find or load main class org.gradle.wrapper.GradleWrapperMain

**السبب**: ملف gradle-wrapper.jar مفقود أو تالف

**الحلول**:

#### الحل الأول: استخدام Android Studio (الأسهل)
```bash
# تشغيل البناء المبسط
build_app_simple.bat

# اختيار الخيار 1 لفتح Android Studio
```

#### الحل الثاني: تحميل Gradle تلقائياً
```bash
# تشغيل سكريبت التحميل التلقائي
download_gradle.bat
```

#### الحل الثالث: التحميل اليدوي
1. اذهب إلى: https://services.gradle.org/distributions/
2. حمل `gradle-8.2-bin.zip`
3. استخرج الملفات
4. انسخ `gradle-wrapper.jar` إلى `android/gradle/wrapper/`

### 2. خطأ: Java not found

**السبب**: Java غير مثبت أو غير موجود في PATH

**الحل**:
```bash
# تحقق من وجود Java
java -version

# إذا لم يكن موجود، حمل وثبت Java من:
# https://adoptium.net/
# أو
# https://www.oracle.com/java/technologies/downloads/
```

**إعداد JAVA_HOME**:
```bash
# Windows
set JAVA_HOME=C:\Program Files\Java\jdk-11.0.x
set PATH=%JAVA_HOME%\bin;%PATH%
```

### 3. خطأ: Android SDK not found

**السبب**: Android SDK غير مثبت أو غير مُعرف

**الحل**:
```bash
# تثبيت Android Studio (يتضمن SDK)
# https://developer.android.com/studio

# أو إعداد ANDROID_HOME يدوياً
set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
set PATH=%ANDROID_HOME%\tools;%ANDROID_HOME%\platform-tools;%PATH%
```

### 4. خطأ: Build failed - Compilation error

**السبب**: أخطاء في الكود أو إعدادات خاطئة

**الحلول**:
```bash
# تنظيف المشروع
cd android
gradlew clean

# إعادة البناء
gradlew assembleDebug

# إذا استمر الخطأ، تحقق من:
# 1. إصدار Android SDK
# 2. إصدار Build Tools
# 3. إعدادات build.gradle
```

### 5. خطأ: Keystore not found

**السبب**: ملف keystore غير موجود للبناء Release

**الحل**:
```bash
# إنشاء keystore جديد
create_keystore.bat

# أو تعديل build.gradle لإزالة إعدادات التوقيع مؤقتاً
```

### 6. خطأ: Permission denied

**السبب**: عدم وجود صلاحيات كافية

**الحل**:
```bash
# تشغيل Command Prompt كمدير
# Right-click -> Run as Administrator

# أو إعطاء صلاحيات للملفات
chmod +x android/gradlew
```

### 7. خطأ: Network timeout

**السبب**: مشاكل في الاتصال بالإنترنت

**الحل**:
```bash
# تحقق من الاتصال بالإنترنت
ping google.com

# إعداد proxy إذا لزم الأمر
# في gradle.properties:
systemProp.http.proxyHost=proxy.company.com
systemProp.http.proxyPort=8080
```

## 🛠️ حلول متقدمة

### إعادة إعداد المشروع بالكامل

```bash
# حذف مجلدات البناء
rmdir /s android\app\build
rmdir /s android\.gradle

# إعادة تحميل dependencies
cd android
gradlew --refresh-dependencies
gradlew clean
gradlew assembleDebug
```

### تحديث Gradle

```bash
# في android/gradle/wrapper/gradle-wrapper.properties
# غير السطر:
distributionUrl=https\://services.gradle.org/distributions/gradle-8.4-bin.zip
```

### إصلاح مشاكل الذاكرة

```bash
# في android/gradle.properties
# أضف:
org.gradle.jvmargs=-Xmx4g -XX:MaxPermSize=512m
org.gradle.parallel=true
org.gradle.daemon=true
```

## 📱 مشاكل التطبيق

### التطبيق لا يعمل على الجهاز

**التحقق من**:
1. إصدار Android (يجب أن يكون 7.0+)
2. تفعيل "Unknown Sources" للتثبيت
3. مساحة كافية على الجهاز

### واجهة المستخدم لا تظهر بشكل صحيح

**الحل**:
```bash
# تحقق من ملف index.html في assets
# تأكد من نسخه بشكل صحيح
copy index.html android\app\src\main\assets\index.html
```

### كلمات المرور لا تُحفظ

**السبب**: مشاكل في localStorage

**الحل**: تحقق من إعدادات WebView في MainActivity.java

## 🔍 أدوات التشخيص

### فحص ملفات المشروع

```bash
# تحقق من وجود الملفات المطلوبة
dir android\app\src\main\assets\index.html
dir android\gradle\wrapper\gradle-wrapper.jar
dir android\gradlew.bat
```

### فحص إعدادات Java

```bash
java -version
javac -version
echo %JAVA_HOME%
```

### فحص إعدادات Android

```bash
echo %ANDROID_HOME%
adb version
```

## 📞 طلب المساعدة

### معلومات مطلوبة عند طلب المساعدة

1. **نظام التشغيل**: Windows 10/11
2. **إصدار Java**: `java -version`
3. **رسالة الخطأ الكاملة**
4. **الخطوات التي اتبعتها**
5. **لقطة شاشة للخطأ**

### طرق التواصل

- 📧 البريد الإلكتروني: [your-email]
- 💬 واتساب: +************
- 📱 تليجرام: [your-telegram]

### الموارد المفيدة

- [Android Developer Documentation](https://developer.android.com/docs)
- [Gradle User Manual](https://docs.gradle.org/current/userguide/userguide.html)
- [Stack Overflow](https://stackoverflow.com/questions/tagged/android)

## ✅ قائمة التحقق السريعة

قبل طلب المساعدة، تأكد من:

- [ ] Java مثبت ويعمل
- [ ] JAVA_HOME مُعرف بشكل صحيح
- [ ] ملف index.html موجود في assets
- [ ] gradle-wrapper.jar موجود
- [ ] اتصال الإنترنت يعمل
- [ ] مساحة كافية على القرص الصلب
- [ ] تشغيل Command Prompt كمدير

---

**نصيحة**: ابدأ دائماً بالحلول البسيطة قبل الانتقال للحلول المعقدة!
