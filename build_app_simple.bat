@echo off
echo ========================================
echo    مولد كلمات المرور المحترف
echo    Password Generator Pro - Simple Build
echo ========================================
echo.

echo [1/3] نسخ ملف HTML إلى مجلد assets...
echo Copying HTML file to assets folder...

if not exist "android\app\src\main\assets" (
    mkdir "android\app\src\main\assets"
)

copy "index.html" "android\app\src\main\assets\index.html" >nul
if %errorlevel% neq 0 (
    echo ❌ خطأ في نسخ ملف HTML
    echo ❌ Error copying HTML file
    pause
    exit /b 1
)

echo ✅ تم نسخ ملف HTML بنجاح
echo ✅ HTML file copied successfully

echo.
echo [2/3] التحقق من Android Studio...
echo Checking for Android Studio...

set "ANDROID_STUDIO_PATH="
for %%i in (
    "C:\Program Files\Android\Android Studio\bin\studio64.exe"
    "C:\Program Files (x86)\Android\Android Studio\bin\studio64.exe"
    "%LOCALAPPDATA%\Android\Android Studio\bin\studio64.exe"
    "%ProgramFiles%\Android\Android Studio\bin\studio64.exe"
) do (
    if exist "%%~i" (
        set "ANDROID_STUDIO_PATH=%%~i"
        goto :found_studio
    )
)

:found_studio
if defined ANDROID_STUDIO_PATH (
    echo ✅ تم العثور على Android Studio
    echo ✅ Android Studio found
    echo المسار: %ANDROID_STUDIO_PATH%
    echo Path: %ANDROID_STUDIO_PATH%
) else (
    echo ⚠️  لم يتم العثور على Android Studio
    echo ⚠️  Android Studio not found
)

echo.
echo [3/3] خيارات البناء المتاحة:
echo Available build options:
echo.
echo 1. فتح المشروع في Android Studio (مستحسن)
echo    Open project in Android Studio (Recommended)
echo.
echo 2. بناء يدوي باستخدام Gradle
echo    Manual build using Gradle
echo.
echo 3. تحميل وإعداد Gradle تلقائياً
echo    Download and setup Gradle automatically
echo.
set /p choice="اختر الخيار (1, 2, أو 3): Choose option (1, 2, or 3): "

if "%choice%"=="1" (
    echo فتح Android Studio...
    echo Opening Android Studio...
    if defined ANDROID_STUDIO_PATH (
        start "" "%ANDROID_STUDIO_PATH%" "%CD%\android"
        echo.
        echo ✅ تم فتح Android Studio
        echo ✅ Android Studio opened
        echo.
        echo في Android Studio:
        echo In Android Studio:
        echo 1. انتظر حتى ينتهي التحميل والفهرسة
        echo    Wait for loading and indexing to complete
        echo 2. اذهب إلى Build > Generate Signed Bundle/APK
        echo    Go to Build > Generate Signed Bundle/APK
        echo 3. اختر APK أو Android App Bundle
        echo    Choose APK or Android App Bundle
        echo 4. اتبع التعليمات لإنشاء keystore
        echo    Follow instructions to create keystore
    ) else (
        echo ❌ لم يتم العثور على Android Studio
        echo ❌ Android Studio not found
        echo.
        echo يرجى تثبيت Android Studio من:
        echo Please install Android Studio from:
        echo https://developer.android.com/studio
    )
) else if "%choice%"=="2" (
    echo محاولة البناء اليدوي...
    echo Attempting manual build...
    cd android
    
    echo تحقق من وجود gradlew...
    echo Checking for gradlew...
    
    if exist "gradlew.bat" (
        echo تشغيل gradlew...
        echo Running gradlew...
        gradlew.bat assembleDebug
        if %errorlevel% equ 0 (
            echo ✅ تم البناء بنجاح!
            echo ✅ Build successful!
            echo.
            echo ملف APK موجود في:
            echo APK file location:
            echo android\app\build\outputs\apk\debug\app-debug.apk
        ) else (
            echo ❌ فشل البناء
            echo ❌ Build failed
            echo.
            echo جرب الخيار 3 لإعداد Gradle تلقائياً
            echo Try option 3 to setup Gradle automatically
        )
    ) else (
        echo ❌ gradlew.bat غير موجود
        echo ❌ gradlew.bat not found
        echo.
        echo جرب الخيار 3 لإعداد Gradle
        echo Try option 3 to setup Gradle
    )
    cd ..
) else if "%choice%"=="3" (
    echo إعداد Gradle تلقائياً...
    echo Setting up Gradle automatically...
    echo.
    echo هذا الخيار يتطلب اتصال إنترنت
    echo This option requires internet connection
    echo.
    echo سيتم تحميل وإعداد Gradle...
    echo Gradle will be downloaded and configured...
    echo.
    pause
    call setup_gradle.bat
) else (
    echo اختيار غير صحيح
    echo Invalid choice
)

echo.
echo ملاحظات مهمة:
echo Important notes:
echo.
echo 1. للنشر على Play Store، ستحتاج ملف AAB:
echo    For Play Store publishing, you need AAB file:
echo    gradlew bundleRelease
echo.
echo 2. لإنشاء keystore للتوقيع:
echo    To create keystore for signing:
echo    create_keystore.bat
echo.
echo 3. للمساعدة، راجع:
echo    For help, check:
echo    README.md و QUICK_START.md
echo.

pause
