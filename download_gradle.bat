@echo off
echo ========================================
echo    تحميل وإعداد Gradle
echo    Download and Setup Gradle
echo ========================================
echo.

echo هذا السكريبت سيقوم بتحميل Gradle تلقائياً
echo This script will download Gradle automatically
echo.

echo متطلبات:
echo Requirements:
echo - اتصال إنترنت / Internet connection
echo - PowerShell (متوفر في Windows 10+)
echo.

set /p confirm="هل تريد المتابعة؟ (y/n): Continue? (y/n): "
if /i not "%confirm%"=="y" (
    echo تم الإلغاء
    echo Cancelled
    pause
    exit /b 0
)

echo.
echo [1/5] إنشاء مجلدات...
echo Creating directories...

if not exist "android\gradle\wrapper" (
    mkdir "android\gradle\wrapper"
)

echo [2/5] تحميل Gradle Wrapper JAR...
echo Downloading Gradle Wrapper JAR...

powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://github.com/gradle/gradle/raw/v8.2.0/gradle/wrapper/gradle-wrapper.jar' -OutFile 'android\gradle\wrapper\gradle-wrapper.jar'}"

if %errorlevel% neq 0 (
    echo ❌ فشل تحميل gradle-wrapper.jar
    echo ❌ Failed to download gradle-wrapper.jar
    echo.
    echo جرب تحميل الملف يدوياً من:
    echo Try downloading manually from:
    echo https://github.com/gradle/gradle/raw/v8.2.0/gradle/wrapper/gradle-wrapper.jar
    echo.
    echo وضعه في: android\gradle\wrapper\gradle-wrapper.jar
    echo Place it in: android\gradle\wrapper\gradle-wrapper.jar
    pause
    exit /b 1
)

echo ✅ تم تحميل gradle-wrapper.jar
echo ✅ gradle-wrapper.jar downloaded

echo.
echo [3/5] إنشاء gradle-wrapper.properties...
echo Creating gradle-wrapper.properties...

echo distributionBase=GRADLE_USER_HOME > android\gradle\wrapper\gradle-wrapper.properties
echo distributionPath=wrapper/dists >> android\gradle\wrapper\gradle-wrapper.properties
echo distributionUrl=https\://services.gradle.org/distributions/gradle-8.2-bin.zip >> android\gradle\wrapper\gradle-wrapper.properties
echo networkTimeout=10000 >> android\gradle\wrapper\gradle-wrapper.properties
echo zipStoreBase=GRADLE_USER_HOME >> android\gradle\wrapper\gradle-wrapper.properties
echo zipStorePath=wrapper/dists >> android\gradle\wrapper\gradle-wrapper.properties

echo ✅ تم إنشاء gradle-wrapper.properties
echo ✅ gradle-wrapper.properties created

echo.
echo [4/5] إنشاء gradlew.bat...
echo Creating gradlew.bat...

echo @rem > android\gradlew.bat
echo @rem Copyright 2015 the original author or authors. >> android\gradlew.bat
echo @rem >> android\gradlew.bat
echo @if "%%DEBUG%%"=="" @echo off >> android\gradlew.bat
echo @rem ########################################################################## >> android\gradlew.bat
echo @rem >> android\gradlew.bat
echo @rem  Gradle startup script for Windows >> android\gradlew.bat
echo @rem >> android\gradlew.bat
echo @rem ########################################################################## >> android\gradlew.bat
echo. >> android\gradlew.bat
echo @rem Set local scope for the variables with windows NT shell >> android\gradlew.bat
echo if "%%OS%%"=="Windows_NT" setlocal >> android\gradlew.bat
echo. >> android\gradlew.bat
echo set DIRNAME=%%~dp0 >> android\gradlew.bat
echo if "%%DIRNAME%%"=="" set DIRNAME=. >> android\gradlew.bat
echo set APP_BASE_NAME=%%~n0 >> android\gradlew.bat
echo set APP_HOME=%%DIRNAME%% >> android\gradlew.bat
echo. >> android\gradlew.bat
echo @rem Add default JVM options here. >> android\gradlew.bat
echo set DEFAULT_JVM_OPTS="-Xmx64m" "-Xms64m" >> android\gradlew.bat
echo. >> android\gradlew.bat
echo @rem Find java.exe >> android\gradlew.bat
echo if defined JAVA_HOME goto findJavaFromJavaHome >> android\gradlew.bat
echo. >> android\gradlew.bat
echo set JAVA_EXE=java.exe >> android\gradlew.bat
echo %%JAVA_EXE%% -version ^>NUL 2^>^&1 >> android\gradlew.bat
echo if %%ERRORLEVEL%% equ 0 goto execute >> android\gradlew.bat
echo. >> android\gradlew.bat
echo echo. >> android\gradlew.bat
echo echo ERROR: JAVA_HOME is not set and no 'java' command could be found in your PATH. >> android\gradlew.bat
echo goto fail >> android\gradlew.bat
echo. >> android\gradlew.bat
echo :findJavaFromJavaHome >> android\gradlew.bat
echo set JAVA_HOME=%%JAVA_HOME:"=%% >> android\gradlew.bat
echo set JAVA_EXE=%%JAVA_HOME%%/bin/java.exe >> android\gradlew.bat
echo. >> android\gradlew.bat
echo if exist "%%JAVA_EXE%%" goto execute >> android\gradlew.bat
echo. >> android\gradlew.bat
echo echo ERROR: JAVA_HOME is set to an invalid directory: %%JAVA_HOME%% >> android\gradlew.bat
echo goto fail >> android\gradlew.bat
echo. >> android\gradlew.bat
echo :execute >> android\gradlew.bat
echo set CLASSPATH=%%APP_HOME%%\gradle\wrapper\gradle-wrapper.jar >> android\gradlew.bat
echo "%%JAVA_EXE%%" %%DEFAULT_JVM_OPTS%% %%JAVA_OPTS%% %%GRADLE_OPTS%% "-Dorg.gradle.appname=%%APP_BASE_NAME%%" -classpath "%%CLASSPATH%%" org.gradle.wrapper.GradleWrapperMain %%* >> android\gradlew.bat
echo. >> android\gradlew.bat
echo :end >> android\gradlew.bat
echo if %%ERRORLEVEL%% equ 0 goto mainEnd >> android\gradlew.bat
echo. >> android\gradlew.bat
echo :fail >> android\gradlew.bat
echo exit /b 1 >> android\gradlew.bat
echo. >> android\gradlew.bat
echo :mainEnd >> android\gradlew.bat
echo if "%%OS%%"=="Windows_NT" endlocal >> android\gradlew.bat

echo ✅ تم إنشاء gradlew.bat
echo ✅ gradlew.bat created

echo.
echo [5/5] اختبار Gradle...
echo Testing Gradle...

cd android
gradlew.bat --version

if %errorlevel% equ 0 (
    echo.
    echo ✅ تم إعداد Gradle بنجاح!
    echo ✅ Gradle setup successful!
    echo.
    echo يمكنك الآن تشغيل:
    echo You can now run:
    echo gradlew assembleDebug
    echo.
) else (
    echo ❌ فشل في إعداد Gradle
    echo ❌ Failed to setup Gradle
    echo.
    echo تأكد من:
    echo Make sure:
    echo 1. Java مثبت ومتاح في PATH
    echo    Java is installed and available in PATH
    echo 2. اتصال الإنترنت يعمل
    echo    Internet connection is working
)

cd ..
pause
