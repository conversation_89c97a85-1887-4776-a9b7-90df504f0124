@echo off
echo ========================================
echo    إصلاح مشكلة Gradle Repositories
echo    Fix Gradle Repositories Issue
echo ========================================
echo.

echo تم إصلاح مشكلة repositories في ملفات Gradle
echo Fixed repositories issue in Gradle files
echo.

echo [1/3] نسخ ملف HTML المحدث...
echo Copying updated HTML file...

if not exist "android\app\src\main\assets" (
    mkdir "android\app\src\main\assets"
)

copy "index.html" "android\app\src\main\assets\index.html" >nul
if %errorlevel% neq 0 (
    echo ❌ خطأ في نسخ ملف HTML
    echo ❌ Error copying HTML file
    pause
    exit /b 1
)

echo ✅ تم نسخ ملف HTML بنجاح
echo ✅ HTML file copied successfully

echo.
echo [2/3] تنظيف مجلدات البناء...
echo Cleaning build directories...

if exist "android\.gradle" (
    rmdir /s /q "android\.gradle"
    echo تم حذف مجلد .gradle
    echo Deleted .gradle folder
)

if exist "android\app\build" (
    rmdir /s /q "android\app\build"
    echo تم حذف مجلد build
    echo Deleted build folder
)

echo.
echo [3/3] محاولة البناء...
echo Attempting build...

cd android

echo تشغيل Gradle Sync...
echo Running Gradle Sync...

gradlew --refresh-dependencies

if %errorlevel% equ 0 (
    echo ✅ تم إصلاح المشكلة بنجاح!
    echo ✅ Issue fixed successfully!
    echo.
    echo الآن يمكنك تشغيل:
    echo Now you can run:
    echo gradlew assembleDebug
    echo.
    
    echo هل تريد بناء التطبيق الآن؟
    echo Do you want to build the app now?
    set /p build_now="(y/n): "
    
    if /i "%build_now%"=="y" (
        echo بناء التطبيق...
        echo Building app...
        gradlew assembleDebug
        
        if %errorlevel% equ 0 (
            echo.
            echo 🎉 تم بناء التطبيق بنجاح!
            echo 🎉 App built successfully!
            echo.
            echo ملف APK موجود في:
            echo APK file location:
            echo android\app\build\outputs\apk\debug\app-debug.apk
        ) else (
            echo ❌ فشل في بناء التطبيق
            echo ❌ Failed to build app
        )
    )
) else (
    echo ❌ لا تزال هناك مشكلة
    echo ❌ There's still an issue
    echo.
    echo جرب فتح المشروع في Android Studio:
    echo Try opening the project in Android Studio:
    echo build_app_simple.bat
)

cd ..
echo.
echo اكتمل!
echo Done!
pause
